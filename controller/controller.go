package controller

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	reuse "github.com/libp2p/go-reuseport"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/util/net/etcd"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/graphql/web"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/handler/data"
	"github.com/tigergraph/gus/handler/health"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/runner"
	"github.com/tigergraph/gus/middleware"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/router"
	"github.com/tigergraph/gus/service/cron"
	"github.com/tigergraph/gus/service/gbar"
)

const shutdownTimeout = 5 * time.Minute

type ControllerDependencies struct {
	RequestNewGsqlToken           interfaces.RequestNewGsqlToken
	GSQLAuthenticator             interfaces.GSQLAuthenticator
	ExpCheckInterval              time.Duration
	DaoManager                    interfaces.DaoManager
	DatabaseManager               interfaces.DatabaseManager
	CheckAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc
	TGFileSystem                  interfaces.TGFileSystem
	CntlrClient                   pb.ControllerClient
	SchemaService                 interfaces.SchemaService
	LoadingJobService             interfaces.LoadingJobService
	UpsertSAMLResponse            interfaces.UpsertSAMLResponse
	RequestGSQLClient             interfaces.RequestGSQLClient
	SchemaSyncer                  web.SchemaSyncer
	TokenService                  interfaces.APITokenService
	InsightsService               interfaces.InsightsService
	IFMClient                     pb.InformantClient
	UDFService                    interfaces.UDFService
}

func (c ControllerDependencies) validate() error {
	if c.GSQLAuthenticator == nil {
		return errors.New("GSQLAuthenticator is nil")
	}
	if c.DaoManager == nil {
		return errors.New("DaoManager is nil")
	}
	if c.DatabaseManager == nil {
		return errors.New("DatabaseManager is nil")
	}
	if c.CheckAndCreateCategoryDirFunc == nil {
		return errors.New("CategoryDirCheckerCreator is nil")
	}
	if c.UpsertSAMLResponse == nil {
		return errors.New("UpsertSAMLResponse is nil")
	}
	if c.LoadingJobService == nil {
		return errors.New("LoadingJobService is nil")
	}
	if c.RequestGSQLClient == nil {
		return errors.New("RequestGSQLClient is nil")
	}
	if c.SchemaSyncer == nil {
		return errors.New("SchemaSyncer is nil")
	}
	if c.TokenService == nil {
		return errors.New("TokenService is nil")
	}
	if c.InsightsService == nil {
		return errors.New("InsightsService is nil")
	}

	return nil
}

// Controller is the main resource management service.
type Controller struct {
	Dep ControllerDependencies
	runner.Runner

	cfg    *config.Config
	router *gin.Engine
	server *http.Server
	ready  chan struct{}

	dbService   interfaces.DatabaseManager
	daoManager  interfaces.DaoManager
	cronService *cron.Manager
	gbarService *gbar.Controller
}

// Enforce Controller to implement Runnable.
var _ interfaces.Runnable = (*Controller)(nil)

func ConfigFromFile(cfgFile string, replica int) Configurator {
	return func(c *Controller) error {
		cfg, err := config.New(cfgFile, replica)
		if err != nil {
			log.Errorf("Failed to load config: %v", err)
			return err
		}
		c.cfg = cfg
		return nil
	}
}

type Configurator func(*Controller) error

func New(deps ControllerDependencies, configurator Configurator) (*Controller, error) {
	if err := deps.validate(); err != nil {
		return nil, err
	}
	controller := &Controller{
		Dep:         deps,
		ready:       make(chan struct{}),
		cronService: cron.New(),
	}
	if err := configurator(controller); err != nil {
		return nil, err
	}
	if controller.cfg == nil {
		return nil, errors.New("config is not initialized")
	}

	dbService := deps.DatabaseManager
	daoManager := deps.DaoManager
	controller.cronService = cron.New()
	controller.dbService = dbService
	controller.daoManager = daoManager

	controller.gbarService = gbar.New(controller.cfg, daoManager, controller.cronService, deps.CntlrClient)

	controller.Runner = runner.New(controller)

	deps.CheckAndCreateCategoryDirFunc(controller.cfg, data.GetCategory().LoadingData, "")
	deps.CheckAndCreateCategoryDirFunc(controller.cfg, data.GetCategory().UserIcon, "")
	return controller, nil
}

// SetUp starts sub-services and prepares the Controller.
func (c *Controller) SetUp() error {
	log.SetLevel(c.cfg.GetLogLevel())

	etcdEndpoints, err := c.cfg.GetEtcdEndpoints()
	if err != nil {
		log.Errorf("Failed to get Etcd endpoints: %v", err)
		return err
	}
	log.Infof("Etcd endpoints: %v", etcdEndpoints)

	etcd.Init(
		etcd.WithEndpoints(etcdEndpoints),
		etcd.WithMaxMsgSizeByte(c.cfg.GetEtcdRequestMaxSize()),
	)

	if err := c.dbService.SetUp(); err != nil {
		log.Errorf("Failed to set up database service: %v", err)
		return err
	}
	log.Info("Database service is ready")
	for {
		if err := c.daoManager.SetImport(false); err != nil {
			log.Errorf("Failed to set import status to false: %v", err)
			time.Sleep(time.Second)
		} else {
			break
		}
	}

	if err := c.Dep.LoadingJobService.SetUp(); err != nil {
		log.Errorf("Failed to set up loading job service: %v", err)
		return err
	}
	log.Info("Loading job service is ready")

	if err := c.cronService.AsyncStart(); err != nil {
		log.Errorf("Failed to start cron service: %v", err)
		return err
	}
	log.Info("Cron service is running")
	if err := c.addCronJobs(); err != nil {
		log.Errorf("Failed to add cron jobs: %v", err)
		return err
	}

	if err := c.gbarService.SetUp(); err != nil {
		log.Errorf("Failed to set up GBAR service: %v", err)
		return err
	}
	log.Info("GBAR service is ready")

	c.setupGin()

	return nil
}

func (c *Controller) addCronJobs() error {
	if _, err := c.cronService.AddJob(createTempDirCleanJob(c.cfg)); err != nil {
		return err
	}

	return nil
}

func (c *Controller) setupGin() {
	if !log.IsDebugLevel() {
		gin.SetMode(gin.ReleaseMode)
	} else {
		// Write debug messages according to our format.
		gin.DefaultWriter = io.Discard
		gin.DebugPrintRouteFunc = func(httpMethod, absPath, handlerName string, numHandlers int) {
			log.Debugf("%-6s %-25s --> %s (%d handlers)", httpMethod, absPath, handlerName, numHandlers)
		}
	}

	c.router = gin.New()
	c.router.HandleMethodNotAllowed = true

	skipLogPaths := []string{
		"/api/ping",
		"/api/ping2",
		"/api/version",
	}
	skipLogReqPaths := []string{
		"/api/auth/login",
	}

	c.router.Use(
		mw.Recovery,
		mw.ReqIDSetter,
		mw.ConfigSetter(c.cfg),
		dao.GinCtxSetter(c.daoManager),
		mw.LoggerWithConfig(skipLogPaths, skipLogReqPaths),
		mw.NoCache,
	)

	c.router.NoMethod(mw.NoMethod)
	c.router.NoRoute(mw.NoRoute)

	c.setupCORS()
	c.setupPublicAPIs()
	c.setupPrivateAPIs()
}

func (c *Controller) setupCORS() {
	// Disable: "DISABLE"; Enable white list origins: "ENABLE_WHITE_LIST"; Enable all origins: "ENABLE_ALL"
	corsConfig := cors.Config{
		AllowMethods:     []string{"GET", "POST", "OPTIONS", "PUT", "HEAD", "DELETE", "PATCH"},
		AllowHeaders:     []string{"Origin", "Accept", "Content-Type", "Authorization", "Request-Type", "Request-ID", "GSQL-QueryLocalMemLimitMB", "GSQL-TIMEOUT", "GSQL-REPLICA", "GSQL-ASYNC", "PROFILE"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}
	switch c.cfg.GetEnableCORS() {
	case "ENABLE_WHITE_LIST":
		corsConfig.AllowWildcard = true
		corsConfig.AllowOrigins = []string{
			"https://tgcloud.io",
			"https://tgcloud-dev.com",
			"https://*.tgcloud.io",
			"https://*.tgcloud-dev.com",
		}
		customizedOrigins := c.cfg.GetWhiteListCORS()
		corsConfig.AllowOrigins = append(corsConfig.AllowOrigins, customizedOrigins...)
		c.router.Use(cors.New(corsConfig))
	case "ENABLE_ALL":
		corsConfig.AllowOriginFunc = func(origin string) bool {
			return true
		}
		c.router.Use(cors.New(corsConfig))
	}
}

func (c *Controller) setupPublicAPIs() {
	publicAPI := c.router.Group("/api")
	{
		router.SetHealthRoutes(publicAPI, c.Dep.CntlrClient)
		router.SetAuthRoutes(publicAPI, router.AuthDependencies{
			UpsertSAMLResponse:  c.Dep.UpsertSAMLResponse,
			GSQLAuthenticator:   c.Dep.GSQLAuthenticator,
			RequestNewGsqlToken: c.Dep.RequestNewGsqlToken,
			Config:              c.cfg,
		})
		router.SetAssetsRoutes(publicAPI, c.cfg)
	}
}

func (c *Controller) setupPrivateAPIs() {
	skipAuthRequests := []string{
		"/api/gsql-server/gsql/v1/saml/meta",
		"/api/gsql-server/gsql/v1/saml/authnrequest",
		"/api/gsql-server/gsql/v1/oidc/authnrequest",
		"/api/gsql-server/gsql/scim/v2/Users",
		"/api/v2", // move the authentication to graphql resolver level
		"/api/v2/",
	}

	authTokenAllowedPaths := []string{
		"/api/system",
		"/api/config",
		"/api/restpp/showprocesslistall",
		"/api/v2",
	}

	privateAPI := c.router.Group("/api")
	{
		cookieAuthMiddlware := middleware.NewCookieAuthMiddleware(c.Dep.GSQLAuthenticator)
		basicAuthMiddlware := middleware.NewBasicAuthMiddleware(c.Dep.GSQLAuthenticator)
		apiTokenAuthMiddlware := middleware.NewAPITokenAuthMiddleware(c.Dep.TokenService, c.Dep.GSQLAuthenticator)
		auth0AuthMiddlware := middleware.NewAuth0AuthMiddleware(c.cfg, c.Dep.GSQLAuthenticator)
		authTokenMiddlware := middleware.NewAuthTokenMiddleware(c.cfg.GetAuthToken(), authTokenAllowedPaths)
		gsqlTokenAuthMiddlware := middleware.NewGsqlTokenAuthMiddleware(c.cfg, c.Dep.GSQLAuthenticator)
		kerberosAuthMiddlware := middleware.NewKerberosAuthMiddleware(c.Dep.GSQLAuthenticator, c.cfg)

		privateAPI.Use(
			gbar.GinCtxSetter(c.gbarService),

			mw.ImportGuard,
			mw.GBARGuard,
			mw.Authentication(skipAuthRequests, []interfaces.AuthMiddleware{
				basicAuthMiddlware,
				apiTokenAuthMiddlware,
				auth0AuthMiddlware,
				kerberosAuthMiddlware,
				cookieAuthMiddlware,
				authTokenMiddlware,
				gsqlTokenAuthMiddlware,
			}),
		)

		privateAPI.GET("/ping2", health.Ping)

		router.SetGSQLServerProxyRoutes(privateAPI)
		router.SetRESTPPProxyRoutes(privateAPI)
		router.SetQueryResultCacheRoutes(privateAPI)
		router.SetInformantServerProxyRoutes(privateAPI)
		router.SetConfigRoutes(privateAPI, c.Dep.CntlrClient)
		router.SetServiceRoutes(privateAPI, c.cfg, c.Dep.CntlrClient)
		router.SetDataRoutes(privateAPI, c.Dep.CheckAndCreateCategoryDirFunc)
		router.SetLogRoutes(privateAPI, c.cfg, c.Dep.TGFileSystem, c.Dep.CntlrClient)
		router.SetSystemRoutes(privateAPI, c.Dep.RequestGSQLClient, c.Dep.CntlrClient)
		router.SetGBARRoutes(privateAPI, c.Dep.CntlrClient, c.cfg, c.Dep.DaoManager)
		router.SetQueryRoutes(privateAPI, c.Dep.DaoManager, c.Dep.RequestGSQLClient)
		router.SetExplorationResultRoutes(privateAPI)
		router.SetGraphStyleRoutes(privateAPI)
		router.SetLoadingJobRoutes(privateAPI, c.Dep.LoadingJobService, c.Dep.RequestGSQLClient)
		router.SetLoadingJobInfoRoutes(privateAPI)
		router.SetVisualPatternRoutes(privateAPI)
		router.SetDataSourceRoutes(privateAPI)
		router.SetGraphQLRoutes(privateAPI, c.Dep.DaoManager)
		router.SetGraphAlgorithmRoutes(privateAPI, c.Dep.RequestGSQLClient)
		router.SetGraphQLToGSQLRoutes(privateAPI, c.Dep.SchemaSyncer)
		router.SetSchemaRoutes(privateAPI, c.Dep.SchemaService)
		router.SetSchemaGenerationRoutes(privateAPI)
		router.SetUpgradeToolsRoutes(privateAPI, c.Dep.CntlrClient)
		router.SetHealthCheckRoutes(privateAPI, c.Dep.CntlrClient, c.Dep.IFMClient)
		router.SetUDFRouters(privateAPI, c.Dep.UDFService)

		deps := graphql.Deps{
			T2pClient:  c.Dep.CntlrClient,
			Config:     c.cfg,
			DaoManager: c.Dep.DaoManager,
		}
		router.SetTokenRoutes(privateAPI, c.Dep.InsightsService, deps)
		router.SetUserPreferenceRoutes(privateAPI)
	}
}

// Run begins to serve HTTP endpoints.
func (c *Controller) Run(ctx context.Context) error {
	startTime := time.Now()
	addr := fmt.Sprintf("127.0.0.1:%d", c.cfg.GetPort())
	c.server = &http.Server{
		Addr:              addr,
		Handler:           c.router,
		ReadHeaderTimeout: 5 * time.Second,
	}

	ln, err := reuse.Listen("tcp", addr)
	log.Infof("server listen in %s", time.Since(startTime))
	if err != nil {
		log.Errorf("Could not listen on %s: %v", addr, err)
		return err
	}

	go func() {
		log.Infof("Listening and serving HTTP on %s", addr)

		c.ready <- struct{}{}
		log.Infof("server ready in %s", time.Since(startTime))

		if err := c.server.Serve(ln); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Could not serve on %s: %v", addr, err)
		}
	}()

	<-ctx.Done()
	return nil
}

// Ready returns a channel that signals when the Controller is ready.
func (c *Controller) Ready() chan struct{} {
	return c.ready
}

// TearDown stops the server and sub-services.
func (c *Controller) TearDown() {
	if c.server != nil {
		log.Info("Server is shutting down...")
		ctx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
		defer cancel()

		if err := c.server.Shutdown(ctx); err != nil {
			log.Errorf("Could not gracefully shutdown the server: %v", err)
		}
		log.Info("Server stopped")
	}

	log.Info("Cron service is shutting down...")
	_ = c.cronService.Stop()
	log.Info("Cron service stopped")

	log.Info("Loading job service is shutting down...")
	c.Dep.LoadingJobService.TearDown()
	log.Info("Loading job service stopped")

	log.Info("GBAR Service is shutting down...")
	c.gbarService.TearDown()
	log.Info("GBAR service stopped")

	log.Info("Database service is shutting down...")
	c.dbService.TearDown()
	log.Info("Database service stopped")
}

func (c *Controller) Router() *gin.Engine {
	return c.router
}

func (c *Controller) SetCfg(cfg config.Config) {
	c.cfg = &cfg
}
