package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	gojwt "github.com/golang-jwt/jwt/v5"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
)

const (
	CookieName            = "TigerGraphApp"
	ctxKeyIsSuperUser     = "IsSuperUser"
	ctxKeyPrivileges      = "Privileges"
	ctxKeyRoles           = "Roles"
	ctxKeyUsername        = "Username"
	ctxKeyRealUsername    = "RealUsername" // for saml logged in user, username is __GSQL__saml
	ctxKeyPassword        = "Password"
	ctxKeySessionID       = "SessionID"
	ctxKeyUserInfo        = "UserInfo"
	ctxKeyAuthType        = "AuthType"
	ctxKeyUserCredentials = "UserCredentials"
)

const DefaultGsqlIssuer = "TigerGraph"

var gsqlAuthError = errors.New("GSQL authentication failed.")
var auth0AuthError = errors.New("Auth0 authentication failed")
var passwordExpiredError = errors.New("Password has expired.")

func GSQLAuthError() error {
	return gsqlAuthError
}
func PasswordExpiredError() error {
	return passwordExpiredError
}

func Auth0AuthError() error {
	return auth0AuthError
}

func pathPrefixWith(path string, basicTokenAuthPaths []string) string {
	for _, p := range basicTokenAuthPaths {
		if strings.HasPrefix(path, p) {
			return p
		}
	}

	return ""
}

type CookieAuthMiddleware struct {
	gsqlAuthenticator interfaces.GSQLAuthenticator
}

func NewCookieAuthMiddleware(gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	return &CookieAuthMiddleware{gsqlAuthenticator: gsqlAuthenticator}
}

func (m *CookieAuthMiddleware) Allow(c *gin.Context) bool {
	if sessionID, _ := c.Cookie(CookieName); sessionID != "" {
		return true
	}
	return false
}

func (m *CookieAuthMiddleware) GetSessionID(c *gin.Context) string {
	return "cookie"
}

func (m *CookieAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	token, _ := c.Cookie(CookieName)
	cfg := GetConfig(c)
	creds := &model.UserCredentials{GsqlToken: token, AuthType: model.GsqlTokenAuthType}
	userInfo, err := m.gsqlAuthenticator(c, cfg, creds, false)
	if err != nil {
		log.Warn(c, "GSQL authentication failed: ", err)
		if strings.Contains(strings.ToLower(err.Error()), "password has expired") {
			return nil, PasswordExpiredError()
		}
		return nil, GSQLAuthError()
	}
	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds}, nil

}

type BasicAuthMiddleware struct {
	gsqlAuthenticator interfaces.GSQLAuthenticator
}

func NewBasicAuthMiddleware(gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	return &BasicAuthMiddleware{gsqlAuthenticator: gsqlAuthenticator}
}

func (m *BasicAuthMiddleware) Allow(c *gin.Context) bool {
	authorization := c.Request.Header.Get("Authorization")
	return strings.HasPrefix(authorization, "Basic")
}

func (m *BasicAuthMiddleware) GetSessionID(c *gin.Context) string {
	if username, _, ok := c.Request.BasicAuth(); ok {
		return fmt.Sprintf("Basic %s", username)
	}
	return ""
}

func (m *BasicAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	if username, password, ok := c.Request.BasicAuth(); ok {
		creds := &model.UserCredentials{Username: username, Password: password, AuthType: model.BasicAuthType}
		userInfo, err := m.gsqlAuthenticator(c, GetConfig(c), creds, false)
		if err != nil {
			log.Warn(c, "authentication failed with Basic token: ", err)
			// Abort(c, http.StatusBadRequest, "your basic token is invalid")
			return nil, err
		} else {
			return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds}, nil
		}

	} else {
		log.Warn(c, "missing basic token for authentication")
		// Abort(c, http.StatusBadRequest, "your basic token is invalid")
		return nil, errors.New("missing basic token for authentication")
	}

}

type APITokenAuthMiddleware struct {
	apiTokenService   interfaces.APITokenService
	gsqlAuthenticator interfaces.GSQLAuthenticator
}

func NewAPITokenAuthMiddleware(apiTokenService interfaces.APITokenService, gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	return &APITokenAuthMiddleware{apiTokenService: apiTokenService, gsqlAuthenticator: gsqlAuthenticator}
}

func (m *APITokenAuthMiddleware) Allow(c *gin.Context) bool {
	authorization := c.Request.Header.Get("Authorization")
	return strings.HasPrefix(authorization, "Token")
}

func (m *APITokenAuthMiddleware) GetSessionID(c *gin.Context) string {
	// trim the latest 12 characters as token is sensitive information
	token := c.Request.Header.Get("Authorization")
	parts := strings.Split(token, "-")
	if len(parts) < 2 {
		return ""
	}

	return strings.Join(parts[:len(parts)-1], "-")
}

func (m *APITokenAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	token := c.Request.Header.Get("Authorization")
	token = strings.TrimPrefix(token, "Token ")
	creds, err := m.apiTokenService.Parse(c, token)
	if err != nil {
		log.Warn(c, "failed to parse API token: ", err)
		// Abort(c, http.StatusBadRequest, "API token is invalid: "+err.Error())
		return nil, err
	}
	userInfo, err := m.gsqlAuthenticator(c, GetConfig(c), creds, false)
	if err != nil {
		log.Warn(c, "authentication failed with API token: ", err)
		// Abort(c, http.StatusBadRequest, "API token is invalid: "+err.Error())
		return nil, err
	}
	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds}, nil
}

type AuthTokenMiddleware struct {
	authToken    string
	allowedPaths []string
}

func NewAuthTokenMiddleware(authToken string, allowedPaths []string) interfaces.AuthMiddleware {
	return &AuthTokenMiddleware{authToken: authToken, allowedPaths: allowedPaths}
}

func (m *AuthTokenMiddleware) Allow(c *gin.Context) bool {
	authorization := c.Request.Header.Get("Authorization")
	authToken := strings.TrimPrefix(authorization, "Bearer ")
	// use len(authToken) == 32 to check if this is auth_token or id_token(auth0).
	// a auth_token is always 32 characters long.
	return strings.HasPrefix(authorization, "Bearer") &&
		len(authToken) == 32 &&
		len(pathPrefixWith(c.Request.URL.Path, m.allowedPaths)) != 0
}

func (m *AuthTokenMiddleware) GetSessionID(c *gin.Context) string {
	return "authToken"
}

func (m *AuthTokenMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	authorization := c.Request.Header.Get("Authorization")
	authToken := strings.TrimPrefix(authorization, "Bearer ")

	if authToken == m.authToken {
		return &model.UserInfoWithCredentials{
			UserInfo: model.UserInfo{
				Name:        "tigergraph",
				IsSuperUser: true,
			},
			UserCredentials: model.UserCredentials{
				AuthToken: m.authToken,
				AuthType:  model.AuthTokenAuthType,
			},
		}, nil
	}

	return nil, errors.New("auth token is invalid")
}

func authorize(c *gin.Context, u *model.UserInfoWithCredentials) {
	c.Set(ctxKeyIsSuperUser, u.IsSuperUser)
	c.Set(ctxKeyPrivileges, u.Privileges)
	c.Set(ctxKeyRoles, u.UserInfo.Roles)
	c.Set(ctxKeyUsername, u.Username)
	c.Set(ctxKeyRealUsername, u.UserInfo.Name)
	c.Set(ctxKeyPassword, u.Password)
	c.Set(ctxKeyUserCredentials, &u.UserCredentials)
	c.Set(ctxKeyUserInfo, &u.UserInfo)
	c.Set(ctxKeyAuthType, u.AuthType)
}

// Authentication returns a middleware that authenticates the user.
func Authentication(
	skipPaths []string,
	authMiddlewares []interfaces.AuthMiddleware,
) gin.HandlerFunc {

	return func(c *gin.Context) {
		// Authenticate only when path is not being skipped.
		shouldSkip := false
		for _, path := range skipPaths {
			if strings.HasPrefix(c.Request.URL.Path, path) {
				shouldSkip = true
				break
			}
		}

		for _, middleware := range authMiddlewares {
			if !middleware.Allow(c) {
				continue
			}

			c.Set(ctxKeySessionID, middleware.GetSessionID(c))
			u, err := middleware.AuthUser(c)
			if err != nil {
				if shouldSkip { // ignore authentication error when path is being skipped
					return
				}

				if err == PasswordExpiredError() {
					Abort(c, http.StatusUnauthorized, err.Error())
				} else if err == GSQLAuthError() {
					Abort(c, http.StatusUnauthorized, "You are not authorized to use this API.")
				} else if err == Auth0AuthError() {
					Abort(c, http.StatusUnauthorized, err.Error())
				} else {
					Abort(c, http.StatusInternalServerError, err.Error())
				}
				return

			}
			authorize(c, u)
			return
		}

		if !shouldSkip {
			Abort(c, http.StatusUnauthorized, "You are not authorized to use this API.")
		}
	}

}

type SimpleAuthResponse struct {
	Results interface{} `json:"results"` // need to define Results as interface{} as it can be a empty string or a map
	Error   bool        `json:"error"`
	Message string      `json:"message"`
}

// https://graphsql.atlassian.net/browse/APPS-2581 Add userLogin for login request to create audit log in gsql.
func AuthenticateWithGSQL(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		fmt.Sprintf("/gsql/v1/auth/simple?userLogin=%v", userLogin),
	)

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, requestURL, nil)
	h.SetCredentials(req, creds)
	h.SetFromGraphStudio(req)
	req.Header.Set("Content-Type", "text/plain;charset=UTF-8")
	h.SetForwardedForFromContext(c, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})

	resp, err := client.Do(req)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get response from GSQL server.")
	}
	log.Debug(c, resp.Status)

	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to read response from GSQL server.")
	}

	info := &SimpleAuthResponse{}
	if err := json.Unmarshal(body, info); err != nil {
		log.Warn(c, string(body), resp.Status)
		return nil, errors.New("Failed to unmarshal response from GSQL server.")
	}

	if info.Error {
		return nil, errors.New(info.Message)
	}

	// convert map[string]interface{} to model.UserInfo
	jsonData, err := json.Marshal(info.Results)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to marshal results.")
	}
	var user model.UserInfo
	err = json.Unmarshal(jsonData, &user)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to unmarshal results.")
	}

	return &user, nil
}

type GsqlAuthToken struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
	Token   string `json:"token"`
}

// RequestNewGsqlToken requests a new GSQL token from the GSQL server
func RequestNewGsqlToken(c context.Context, gsqlUsername, gsqlPassword, graphName string, lifetime int64) (string, error) {
	cfg := GetConfig(c.(*gin.Context))
	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal restpp requests.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})
	requestURL := fmt.Sprintf(
		"%s://%s:%d/gsql/v1/tokens",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
	)
	log.Debug(c, requestURL)

	data, err := json.Marshal(struct {
		Lifetime int64  `json:"lifetime"`
		Graph    string `json:"graph,omitempty"`
	}{
		Lifetime: lifetime,
		Graph:    graphName,
	})
	if err != nil {
		return "", errors.WithStack(err)
	}

	req, err := http.NewRequestWithContext(c, http.MethodPost, requestURL, bytes.NewReader(data))
	if err != nil {
		return "", errors.WithStack(err)
	}

	// body using application/json type
	req.Header.Set("Content-Type", "application/json")

	req.SetBasicAuth(gsqlUsername, gsqlPassword)
	h.SetFromGraphStudio(req)
	h.SetForwardedForFromContext(c, req)

	res, err := client.Do(req)
	if err != nil {
		return "", errors.WithStack(err)
	}
	defer func() {
		if res.Body != nil {
			err2 := res.Body.Close()
			if err2 != nil {
				log.Error(c, err2)
			}
		}
	}()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", errors.WithMessage(err, "Failed to get response from GSQL server")
	}
	result := GsqlAuthToken{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", errors.WithMessage(err, "Failed to parse response from GSQL server")
	}
	log.Infof(c, "RequestNewGsqlToken: %v", result.Message)
	if result.Error {
		return "", errors.Errorf("failed to get auth token from gsql: %s", result.Message)
	}
	return result.Token, nil
}

// RequiredAuthToken returns a middleware that requires AuthToken permission.
func RequiredAuthToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		reqToken := c.Request.Header.Get("Authorization")
		authToken := strings.TrimPrefix(reqToken, "Bearer ")

		cfg := GetConfig(c)
		if authToken != cfg.GetAuthToken() {
			c.AbortWithStatusJSON(http.StatusForbidden, model.Response{Message: "You are not authorized to use this internal API. Please use auth token."})

			return
		}
	}

}

// RequiredSuperUser returns a middleware that requires SuperUser permission.
func RequiredSuperUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		isSuperUser := c.GetBool(ctxKeyIsSuperUser)
		if !isSuperUser {
			Abort(c, http.StatusForbidden, "SuperUser permission required.")
			return
		}
	}
}

func HasPrivilege(c *gin.Context, graphName string, privilege model.Privilege) bool {
	privileges := c.MustGet(ctxKeyPrivileges).(model.Privileges)

	// if user has global graph privilege, he will have all graph privileges
	for _, p := range privileges["1"].Privileges {
		if p == privilege {
			return true
		}
	}

	for _, p := range privileges[graphName].Privileges {
		if p == privilege {
			return true
		}
	}
	return false
}

func HasRole(c *gin.Context, graphName string, role string) bool {
	roles := c.MustGet(ctxKeyRoles).(model.Roles)

	for _, p := range roles["1"] {
		if p == role {
			return true
		}
	}

	for _, p := range roles[graphName] {
		if p == role {
			return true
		}
	}
	return false
}

// RequiredPrivileges returns a middleware that whether the user has the required privileges.
func RequiredPrivileges(requiredPrivileges ...model.Privilege) gin.HandlerFunc {
	return func(c *gin.Context) {
		graphName := c.Param("graphName")
		displayGraphName := graphName
		// if graphName is not specified, set to  "1" , which stands for global graph name.
		if graphName == "" {
			graphName = "1"
			displayGraphName = "Global"
		}

		// check if graph exist
		privileges := c.MustGet(ctxKeyPrivileges).(model.Privileges)
		if _, ok := privileges[graphName]; !ok {
			c.AbortWithStatusJSON(http.StatusNotFound, model.Response{Message: fmt.Sprintf("Graph %s does not exist or you don't have privileges on it.", displayGraphName)})
			return
		}

		for _, rp := range requiredPrivileges {
			if !HasPrivilege(c, graphName, rp) {
				c.AbortWithStatusJSON(http.StatusForbidden, model.Response{Message: fmt.Sprintf("%s privilege is required on graph %s.", rp, displayGraphName)})
				return
			}
		}
	}

}

func IsSuperUser(c *gin.Context) bool {
	return c.GetBool(ctxKeyIsSuperUser)
}

func GetSessionID(c *gin.Context) string {
	t, ok := c.Get(ctxKeySessionID)
	if !ok {
		return ""
	}

	return cast.ToString(t)
}

func GetUsername(c *gin.Context) string {
	t, ok := c.Get(ctxKeyUsername)
	if !ok {
		return ""
	}

	return cast.ToString(t)
}

func GetRealUsername(c *gin.Context) string {
	t, ok := c.Get(ctxKeyRealUsername)
	if !ok {
		return ""
	}

	return cast.ToString(t)
}

func GetUserCredentials(c *gin.Context) *model.UserCredentials {
	t, ok := c.Get(ctxKeyUserCredentials)
	if !ok {
		return nil
	}
	return t.(*model.UserCredentials)
}

func GetUserInfo(c *gin.Context) *model.UserInfo {
	t, ok := c.Get(ctxKeyUserInfo)
	if !ok {
		return nil
	}
	return t.(*model.UserInfo)
}

func GetAuthType(c *gin.Context) model.AuthType {
	t, ok := c.Get(ctxKeyAuthType)
	if !ok {
		return ""
	}

	return t.(model.AuthType)
}

type Auth0Middleware struct {
	gsqlAuthenticator interfaces.GSQLAuthenticator
	cfg               *config.Config
	kcPair            map[string]string
}

func NewAuth0AuthMiddleware(cfg *config.Config, gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	kcPair := getKidAndCertPair(cfg.GetOIDCJWKSUrl())
	return &Auth0Middleware{
		gsqlAuthenticator: gsqlAuthenticator,
		cfg:               cfg,
		kcPair:            kcPair,
	}
}

// Active autho middleware only when OIDC is enabled and request has Bearer token
func (i *Auth0Middleware) Allow(c *gin.Context) bool {
	authorization := c.Request.Header.Get("Authorization")
	authToken := strings.TrimPrefix(authorization, "Bearer ")
	token, _ := gojwt.Parse(authToken, nil)
	if token == nil {
		return false
	}

	claims := token.Claims.(gojwt.MapClaims)

	return i.cfg.GetOIDCEnable() &&
		strings.HasPrefix(authorization, "Bearer ") &&
		claims["iss"] == i.cfg.GetOIDCIssuer()
}

func (i *Auth0Middleware) GetSessionID(c *gin.Context) string {
	return "auth0"
}

func (i *Auth0Middleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	tokenStr := c.Request.Header.Get("Authorization")
	tokenStr = strings.TrimPrefix(tokenStr, "Bearer ")

	_, err := gojwt.Parse(tokenStr, func(token *gojwt.Token) (interface{}, error) {
		iss := i.cfg.GetOIDCIssuer()
		claims := token.Claims.(gojwt.MapClaims)
		if claims["iss"] != iss {
			return token, errors.Errorf("invalid issuer: %s", iss)
		}
		cert, ok := i.kcPair[token.Header["kid"].(string)]
		if !ok || cert == "" {
			return token, fmt.Errorf("internal error: failed to get pem cert: %v", "unable to find appropriate key")
		}
		cert = fmt.Sprintf("-----BEGIN CERTIFICATE-----\n%s\n-----END CERTIFICATE-----", cert)
		result, _ := gojwt.ParseRSAPublicKeyFromPEM([]byte(cert))
		return result, nil
	})

	// Check if there was an error in parsing...
	if err != nil {
		log.Warnf(c, "Error parsing token: %v", err)
		return nil, Auth0AuthError()
	}

	creds := &model.UserCredentials{
		Username: i.cfg.GetOIDCBuiltinUser(),
		Password: tokenStr,
		AuthType: model.Auth0AuthType,
	}

	userInfo, err := i.gsqlAuthenticator(c, i.cfg, creds, false)
	if err != nil {
		log.Warn(c, "GSQL authentication failed: ", err)
		return nil, GSQLAuthError()
	}
	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds}, nil
}

type KerberosAuthMiddleware struct {
	gsqlAuthenticator interfaces.GSQLAuthenticator
	cfg               *config.Config
}

func NewKerberosAuthMiddleware(gsqlAuthenticator interfaces.GSQLAuthenticator, cfg *config.Config) interfaces.AuthMiddleware {
	return &KerberosAuthMiddleware{
		gsqlAuthenticator: gsqlAuthenticator,
		cfg:               cfg,
	}
}

func (m *KerberosAuthMiddleware) Allow(c *gin.Context) bool {
	// Only allow if Kerberos is enabled and request has Negotiate authorization
	if !m.cfg.GetKerberosEnable() {
		return false
	}

	authorization := c.Request.Header.Get("Authorization")
	return strings.HasPrefix(authorization, "Negotiate ")
}

func (m *KerberosAuthMiddleware) GetSessionID(c *gin.Context) string {
	return "kerberos"
}

func (m *KerberosAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	authHeader := c.Request.Header.Get("Authorization")
	if !strings.HasPrefix(authHeader, "Negotiate ") {
		return nil, GSQLAuthError()
	}

	// Extract the Kerberos token
	kerberosToken := strings.TrimPrefix(authHeader, "Negotiate ")
	if kerberosToken == "" {
		log.Warn(c, "Empty Kerberos token")
		return nil, GSQLAuthError()
	}

	// Create credentials for GSQL authentication
	creds := &model.UserCredentials{
		Username: m.cfg.GetKerberosBuiltinUser(),
		Password: kerberosToken,
		AuthType: model.KerberosAuthType,
	}

	// Authenticate with GSQL server
	userInfo, err := m.gsqlAuthenticator(c, m.cfg, creds, false)
	if err != nil {
		log.Warn(c, "GSQL Kerberos authentication failed: ", err)
		return nil, GSQLAuthError()
	}

	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds}, nil
}

type Jwks struct {
	Keys []JSONWebKeys `json:"keys"`
}

type JSONWebKeys struct {
	Kty string   `json:"kty"`
	Kid string   `json:"kid"`
	Use string   `json:"use"`
	N   string   `json:"n"`
	E   string   `json:"e"`
	X5c []string `json:"x5c"`
}

func getKidAndCertPair(jwksURL string) map[string]string {
	KCPair := make(map[string]string)

	resp, err := http.Get(jwksURL) //nolint:gosec // the url is from config file
	if err != nil {
		return KCPair
	}
	defer resp.Body.Close()

	var jwks = Jwks{}
	err = json.NewDecoder(resp.Body).Decode(&jwks)
	if err != nil {
		return KCPair
	}

	for _, key := range jwks.Keys {
		if len(key.X5c) > 0 {
			KCPair[key.Kid] = key.X5c[0]
		}
	}

	return KCPair
}

type GsqlTokenAuthMiddleware struct {
	cfg               *config.Config
	gsqlAuthenticator interfaces.GSQLAuthenticator
}

func NewGsqlTokenAuthMiddleware(cfg *config.Config, gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	return &GsqlTokenAuthMiddleware{cfg: cfg, gsqlAuthenticator: gsqlAuthenticator}
}

func (i *GsqlTokenAuthMiddleware) Allow(c *gin.Context) bool {
	authorization := c.Request.Header.Get("Authorization")
	tokenStr := strings.TrimPrefix(authorization, "Bearer ")
	token, _ := gojwt.Parse(tokenStr, nil)
	if token == nil {
		return false
	}
	claims := token.Claims.(gojwt.MapClaims)
	return strings.HasPrefix(authorization, "Bearer ") && claims["iss"] == DefaultGsqlIssuer
}

func (i *GsqlTokenAuthMiddleware) GetSessionID(c *gin.Context) string {
	return "gsql_token"
}

func (i *GsqlTokenAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	tokenStr := c.Request.Header.Get("Authorization")
	tokenStr = strings.TrimPrefix(tokenStr, "Bearer ")

	userInfo, err := i.gsqlAuthenticator(c, i.cfg, &model.UserCredentials{GsqlToken: tokenStr, AuthType: model.GsqlTokenAuthType}, false)
	if err != nil {
		return nil, err
	}

	creds := &model.UserCredentials{
		Username:  userInfo.Name,
		Password:  "",
		GsqlToken: tokenStr,
		AuthType:  model.GsqlTokenAuthType,
	}

	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds}, nil
}
