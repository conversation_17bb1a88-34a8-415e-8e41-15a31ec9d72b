package config

import (
	"fmt"
	"math/rand"
	"net/url"
	"path"
	"strings"
	"sync"
	"time"

	"github.com/tigergraph/cqrs/tutopia/common/config"
	tgCfg "github.com/tigergraph/cqrs/tutopia/common/config"
	"github.com/tigergraph/cqrs/tutopia/common/config/resolver"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
)

const defaultLocalHostname = "127.0.0.1"

type Config struct {
	cfg     *tgCfg.Config
	replica int
	resolv  *resolver.ConfigResolver
	mux     *sync.RWMutex
}

func New(cfgFile string, replica int) (*Config, error) {
	cfg, err := tgCfg.NewConfigFromFile(cfgFile)
	if err != nil {
		return nil, err
	}

	return &Config{
		cfg:     cfg,
		replica: replica,
		resolv:  resolver.NewConfigResolver(cfg),
		mux:     &sync.RWMutex{},
	}, nil
}

func (c *Config) GetConfig() *tgCfg.Config {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg
}

func (c *Config) GetReplica() int {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.replica
}

func (c *Config) GetLogLevel() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.BasicConfig.LogConfig.LogLevel
}

func (c *Config) GetPort() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.Port
}

func (c *Config) GetCookieDuration() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.Cookie.DurationSec
}

func (c *Config) GetCookieSameSite() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.Cookie.SameSite
}

func (c *Config) GetEnableCORS() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.EnableCORS
}

func (c *Config) GetWhiteListCORS() []string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.WhiteListCORS
}

func (c *Config) GetEnableConcurrentSession() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.EnableConcurrentSession
}

func (c *Config) GetEnableGraphStudio() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.EnableGraphStudio
}

func (c *Config) GetEnableInsights() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.EnableInsights
}

func (c *Config) GetEnableGSQLShell() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.EnableGSQLShell
}

func (c *Config) GetEnableGraphQL() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.EnableGraphQL
}

func (c *Config) GetEnableAdminPortal() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.EnableAdminPortal
}

func (c *Config) GetDataDirPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()

	root := c.cfg.ProtoConf.System.DataRoot
	relativePath := c.cfg.ProtoConf.GUI.DataDirRelativePath
	return path.Join(root, relativePath)
}

func (c *Config) GetSystemTempDirPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.System.TempRoot
}

func (c *Config) GetTempDirPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()

	root := c.cfg.ProtoConf.System.TempRoot
	relativePath := c.cfg.ProtoConf.GUI.TempDirRelativePath
	return path.Join(root, relativePath)
}

func (c *Config) GetTempFileMaxDuration() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GUI.TempFileMaxDurationDay
}

func (c *Config) GetHTTPRequestTimeout() time.Duration {
	c.mux.RLock()
	defer c.mux.RUnlock()
	timeout := c.cfg.ProtoConf.GUI.HTTPRequest.TimeoutSec
	return time.Duration(timeout) * time.Second
}

func (c *Config) GetHTTPRequestRetryMax() int {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return int(c.cfg.ProtoConf.GUI.HTTPRequest.RetryMax)
}

func (c *Config) GetHTTPRequestRetryWaitMin() time.Duration {
	c.mux.RLock()
	defer c.mux.RUnlock()
	retryWaitMin := c.cfg.ProtoConf.GUI.HTTPRequest.RetryWaitMinSec
	return time.Duration(retryWaitMin) * time.Second
}

func (c *Config) GetHTTPRequestRetryWaitMax() time.Duration {
	c.mux.RLock()
	defer c.mux.RUnlock()
	retryWaitMax := c.cfg.ProtoConf.GUI.HTTPRequest.RetryWaitMaxSec
	return time.Duration(retryWaitMax) * time.Second
}

func (c *Config) GetRESTPPResponseMaxSize() int64 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return int64(c.cfg.ProtoConf.GUI.RESTPPResponseMaxSizeBytes)
}

func (c *Config) GetAuthToken() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.System.AuthToken
}

func (c *Config) GetLogRootDirPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.System.LogRoot
}

func (c *Config) GetOldGStoreDirPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return path.Join(c.cfg.ProtoConf.System.DataRoot, "gstore", "gbar")
}

func (c *Config) GetBackupLocalEnabled() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.System.Backup.Local.Enable
}

func (c *Config) GetBackupS3Enabled() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.System.Backup.S3.Enable
}

func (c *Config) GetBackupSchedulerEnabled() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.System.Backup.Scheduler.Enable
}

func (c *Config) GetBackupLocalPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.System.Backup.Local.Path
}

func (c *Config) GetSSOBuiltinUser() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Security.SSO.SAML.BuiltinUser
}

func (c *Config) GetGSQLTokenHMACSecret() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GSQL.Token.HMACSecret
}

func (c *Config) GetOIDCEnable() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Security.SSO.OIDC.Enable
}

func (c *Config) GetOIDCIssuer() string {
	c.mux.RLock()
	defer c.mux.RUnlock()

	ssoUrl := c.cfg.ProtoConf.Security.SSO.OIDC.OP.SSOUrl
	u, _ := url.Parse(ssoUrl)
	return fmt.Sprintf("%s://%s/", u.Scheme, u.Host)
}

func (c *Config) GetOIDCJWKSUrl() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Security.SSO.OIDC.OP.JWKSUrl
}

func (c *Config) GetOIDCBuiltinUser() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Security.SSO.OIDC.BuiltinUser
}

func (c *Config) GetKerberosEnable() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Security.Kerberos.EnableSSO
}

func (c *Config) GetKerberosBuiltinUser() string {
	return "__GSQL__negotiate"
}

func (c *Config) GetHostID() (string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()

	hostIDs, err := c.getHostIDsFromReplica(tgServ.GUI, c.replica)
	if err != nil {
		return "", err
	}
	return hostIDs[0], nil
}

func (c *Config) GetAllHostIDs() []string {
	c.mux.RLock()
	defer c.mux.RUnlock()

	hostIDs := make([]string, len(c.cfg.ProtoConf.System.HostList))
	for i, hostInfo := range c.cfg.ProtoConf.System.HostList {
		hostIDs[i] = hostInfo.ID
	}
	return hostIDs
}

func (c *Config) GetAllGUIHostIDs() ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostIDsFromReplica(tgServ.GUI, tgServ.ReplicaAll)
}

func (c *Config) GetAllGSEHostIDsWithSameReplica() ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()

	hostID, err := c.GetHostID()
	if err != nil {
		return nil, err
	}
	nodes := c.cfg.ProtoConf.GSE.BasicConfig.Nodes
	var replica int32
	for _, node := range nodes {
		if node.HostID == hostID {
			replica = int32(node.Replica)
			break
		}
	}
	results := make([]string, 0)
	for _, node := range nodes {
		if node.Replica == replica {
			results = append(results, node.HostID)
		}
	}
	return results, nil
}

func (c *Config) GetAllGUIHostnames() ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()

	hostIDs, err := c.getHostIDsFromReplica(tgServ.GUI, tgServ.ReplicaAll)
	if err != nil {
		return nil, err
	}

	hostnames, err := c.getHostnamesFromIDs(hostIDs)
	if err != nil {
		return nil, err
	}
	return hostnames, nil
}

func (c *Config) GetAllGSQLHostIDs() ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostIDsFromReplica(tgServ.GSQL, tgServ.ReplicaAll)
}

func (c *Config) GetAllGPEHostIDs() ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostIDsFromReplica(tgServ.GPE, tgServ.ReplicaAll)
}

func (c *Config) GetAllEXEHostIDs() ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostIDsFromReplica(tgServ.EXECUTOR, tgServ.ReplicaAll)
}

func (c *Config) GetServDescFromHostID(hostID string) ([]tgServ.ServiceDescriptor, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getServDescFromHostID(tgServ.EXECUTOR, hostID)
}

func (c *Config) GetEtcdEndpoints() ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.resolv.ResolveETCDEndpoints()
}

func (c *Config) ResolveServiceHostID(sd tgServ.ServiceDescriptor) ([]string, error) {
	return c.resolv.ResolveServiceHostID(sd)
}

func (c *Config) GetEtcdRequestMaxSize() int {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return int(c.cfg.ProtoConf.ETCD.MaxRequestBytes)
}

func (c *Config) GetNginxPort() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Nginx.Port
}

func (c *Config) GetGSQLPort() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.GSQL.Port
}

func (c *Config) GetNginxSSLEnabled() bool {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Nginx.SSL.Enable
}

func (c *Config) GetNginxProtocol() string {
	if c.GetNginxSSLEnabled() {
		return "https"
	}
	return "http"
}

func (c *Config) GetControllerPort() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Controller.Port
}

func (c *Config) GetIFMRestPort() int32 {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.cfg.ProtoConf.Informant.RestPort
}

func (c *Config) GetGadminBinPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return path.Join(c.cfg.ProtoConf.System.AppRoot, "cmd", "gadmin")
}

func (c *Config) GetGBARBinPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return path.Join(c.cfg.ProtoConf.System.AppRoot, "cmd", "gbar")
}

func (c *Config) GetGuiBinPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return path.Join(c.cfg.ProtoConf.System.AppRoot, "bin", "gui")
}

func (c *Config) GetGSQLBinPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return path.Join(c.cfg.ProtoConf.System.AppRoot, "cmd", "gsql")
}

func (c *Config) GetGSQLAlgorithmsPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return path.Join(c.cfg.ProtoConf.System.AppRoot, "lib", "gsql-graph-algorithms")
}

func (c *Config) GetAllControllerHostnames() ([]string, error) {
	return c.GetAllHostnamesForService(tgServ.CONTROLLER)
}

func (c *Config) GetAllHostnamesForService(servName tgServ.ServiceName) ([]string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()

	hostIDs, err := c.getHostIDsFromReplica(servName, tgServ.ReplicaAll)
	if err != nil {
		return nil, err
	}

	hostnames, err := c.getHostnamesFromIDs(hostIDs)
	if err != nil {
		return nil, err
	}
	return hostnames, nil
}

func (c *Config) GetGSQLServerHostname() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostnameFromReplica(tgServ.GSQL, c.replica)
}

func (c *Config) GetGUIHostname() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostnameFromReplica(tgServ.GUI, c.replica)
}

func (c *Config) GetIFMHostname() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostnameFromReplica(tgServ.INFORMANT, c.replica)
}

func (c *Config) GetRESTPPHostname() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostnameFromReplica(tgServ.RESTPP, c.replica)
}

func (c *Config) GetKafkaHostName() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.getHostnameFromReplica(tgServ.KAFKA, c.replica)
}

func (c *Config) getKafkaProtocol() string {
	sslEnable := c.cfg.ProtoConf.GetKafka().GetSecurity().GetSSL().Enable
	kafkaProtocol := config.KAFKA_PROTO_PLAINTEXT
	if sslEnable {
		kafkaProtocol = config.KAFKA_PROTO_SSL
	}
	return kafkaProtocol
}

func (c *Config) GetKafkaEndPoints() (string, error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	kafkaAddrs, err := resolver.GetKafkaEndPoints(c.cfg, c.getKafkaProtocol())
	if err != nil {
		return "", err
	}

	kafkaEndpoints := strings.Split(kafkaAddrs, ",")
	return kafkaEndpoints[0], nil
}

func (c *Config) SetEntry(key, value string) error {
	c.mux.Lock()
	defer c.mux.Unlock()
	return c.cfg.SetEntry(key, value)
}

func (c *Config) SetEntries(entries [][]string) error {
	c.mux.Lock()
	defer c.mux.Unlock()
	return c.cfg.SetEntries(entries)
}

func (c *Config) getHostnameFromReplica(servName tgServ.ServiceName, replica int) string {
	hostIDs, err := c.getHostIDsFromReplica(servName, replica)
	if err != nil {
		return defaultLocalHostname
	}
	hostID := hostIDs[0]

	descs, err := c.getServDescFromHostID(servName, hostID)
	if err != nil {
		return defaultLocalHostname
	}
	if len(descs) == 0 {
		hostID, err = c.getRandomHostID(servName)
		if err != nil {
			return defaultLocalHostname
		}
	}

	hostname, err := c.getHostnameFromID(hostID)
	if err != nil {
		return defaultLocalHostname
	}
	return hostname
}

func (c *Config) getHostIDsFromReplica(servName tgServ.ServiceName, replica int) ([]string, error) {
	desc, err := tgServ.NewServiceDescriptor(servName, tgServ.PartitionAll, replica)
	if err != nil {
		return nil, err
	}

	hostIDs, err := c.resolv.ResolveServiceHostID(desc)
	if err != nil {
		return nil, err
	}
	return hostIDs, nil
}

func (c *Config) getServDescFromHostID(servName tgServ.ServiceName, hostID string) ([]tgServ.ServiceDescriptor, error) {
	descs, err := c.resolv.ResolveServiceInstanceByHostID(servName, hostID)
	if err != nil {
		return nil, err
	}
	return descs, nil
}

func (c *Config) getRandomHostID(servName tgServ.ServiceName) (string, error) {
	desc, err := tgServ.NewServiceDescriptor(servName, tgServ.PartitionAll, tgServ.ReplicaAll)
	if err != nil {
		return "", err
	}

	hostIDs, err := c.resolv.ResolveServiceHostID(desc)
	if err != nil {
		return "", err
	}

	//nolint:gosec // this result is not used in a secure application.
	idx := rand.Intn(len(hostIDs))
	return hostIDs[idx], nil
}

func (c *Config) getHostnameFromID(hostID string) (string, error) {
	hostnames, err := c.resolv.ResolveHostIp([]string{hostID})
	if err != nil {
		return "", err
	}
	return hostnames[0], nil
}

func (c *Config) getHostnamesFromIDs(hostIDs []string) ([]string, error) {
	hostnames := make([]string, len(hostIDs))
	for i, id := range hostIDs {
		name, err := c.getHostnameFromID(id)
		if err != nil {
			return nil, err
		}
		hostnames[i] = name
	}
	return hostnames, nil
}

func (c *Config) GetPartitionReplicaCnt(svc tgServ.ServiceName) (partitionCnt int, replicaCnt int, err error) {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return c.resolv.ResolvePartitionReplicaCnt(svc)
}

func (c *Config) GetStartServiceDefaultTimeoutMS() time.Duration {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return time.Duration(c.cfg.ProtoConf.Gadmin.StartServiceDefaultTimeoutMS) * time.Millisecond
}

func (c *Config) GetStopServiceDefaultTimeoutMS() time.Duration {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return time.Duration(c.cfg.ProtoConf.Gadmin.StopServiceDefaultTimeoutMS) * time.Millisecond
}

func (c *Config) GetGSEStopTimeoutMS() time.Duration {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return time.Duration(c.cfg.ProtoConf.GSE.StopTimeoutMS) * time.Millisecond
}

func (c *Config) GetGSELogPath() string {
	c.mux.RLock()
	defer c.mux.RUnlock()
	return path.Join(c.cfg.ProtoConf.System.LogRoot, c.cfg.ProtoConf.GSE.BasicConfig.LogDirRelativePath)
}
